package service

import (
	"context"
	"testing"

	"gold_store/api/common"
	v1 "gold_store/api/gold_store/v1"
)

// TestEMSServiceMethods 测试EMS服务方法
func TestEMSServiceMethods(t *testing.T) {
	// 创建测试服务实例
	service := &Service{} // 简化的服务实例
	ctx := context.Background()

	t.Run("TestQueryTrack", func(t *testing.T) {
		req := &v1.QueryTrackRequest{
			Language:    "zh-CN",
			WaybillNos:  []string{"1"},
			TrackingNos: []string{},
		}

		resp, err := service.QueryTrack(ctx, req)
		if err != nil {
			t.Errorf("QueryTrack failed: %v", err)
			return
		}

		if resp == nil {
			t.Error("QueryTrack returned nil response")
			return
		}

		t.Logf("QueryTrack response: Success=%v, Message=%s, Tracks=%d",
			resp.Success, resp.Message, len(resp.Tracks))

		// 验证响应结构
		if !resp.Success {
			t.Logf("QueryTrack not successful: %s", resp.Message)
		}
	})

	t.Run("TestQueryWaybill", func(t *testing.T) {
		req := &v1.QueryWaybillRequest{
			Language:    "zh-CN",
			WaybillNos:  []string{"1"},
			TrackingNos: []string{},
		}

		resp, err := service.QueryWaybill(ctx, req)
		if err != nil {
			t.Errorf("QueryWaybill failed: %v", err)
			return
		}

		if resp == nil {
			t.Error("QueryWaybill returned nil response")
			return
		}

		t.Logf("QueryWaybill response: Success=%v, Message=%s, Waybills=%d",
			resp.Success, resp.Message, len(resp.Waybills))
	})

	t.Run("TestQueryFee", func(t *testing.T) {
		req := &v1.QueryFeeRequest{
			Language:        "zh-CN",
			ServiceType:     "EMS",
			SenderCountry:   "CN",
			ReceiverCountry: "US",
			Weight:          500,
			Cargo: []*v1.CargoInfo{
				{
					Name:        "Test Item",
					Quantity:    1,
					Weight:      500,
					Value:       1000,
					Currency:    "CNY",
					Origin:      "CN",
					Description: "Test description",
				},
			},
		}

		resp, err := service.QueryFee(ctx, req)
		if err != nil {
			t.Errorf("QueryFee failed: %v", err)
			return
		}

		if resp == nil {
			t.Error("QueryFee returned nil response")
			return
		}

		t.Logf("QueryFee response: Success=%v, Message=%s", resp.Success, resp.Message)
	})

	t.Run("TestHandlePushNotification", func(t *testing.T) {
		req := &v1.PushNotificationRequest{
			Method:           "040002",
			SenderNo:         "1100221653102",
			MsgType:          "0",
			TimeStamp:        "2025-07-23 15:30:00",
			Version:          "V1.0.0",
			LogitcsInterface: "|$4|test_encrypted_data",
			SerialNo:         "test_serial_123",
		}

		resp, err := service.HandlePushNotification(ctx, req)
		if err != nil {
			t.Errorf("HandlePushNotification failed: %v", err)
			return
		}

		if resp == nil {
			t.Error("HandlePushNotification returned nil response")
			return
		}

		t.Logf("HandlePushNotification response: SerialNo=%s, Code=%s, Message=%s",
			resp.SerialNo, resp.Code, resp.CodeMessage)
	})

	t.Run("TestEMSHealth", func(t *testing.T) {
		req := &common.EmptyRequest{}

		resp, err := service.EMSHealth(ctx, req)
		if err != nil {
			t.Errorf("EMSHealth failed: %v", err)
			return
		}

		if resp == nil {
			t.Error("EMSHealth returned nil response")
			return
		}

		if !resp.Success {
			t.Errorf("EMSHealth not successful: %s", resp.Message)
		}

		t.Logf("EMSHealth response: Success=%v, Message=%s, Status=%s",
			resp.Success, resp.Message, resp.Data.Status)
	})
}

// TestEMSHealthOnly 测试EMS健康检查（不依赖外部服务）
func TestEMSHealthOnly(t *testing.T) {
	service := &Service{}
	ctx := context.Background()

	req := &common.EmptyRequest{}
	resp, err := service.EMSHealth(ctx, req)
	if err != nil {
		t.Errorf("EMSHealth failed: %v", err)
		return
	}

	if resp == nil {
		t.Error("EMSHealth returned nil response")
		return
	}

	if !resp.Success {
		t.Errorf("EMSHealth not successful: %s", resp.Message)
	}

	t.Logf("EMSHealth response: Success=%v, Message=%s, Status=%s",
		resp.Success, resp.Message, resp.Data.Status)
}

// BenchmarkEMSServiceMethods 性能测试
func BenchmarkEMSServiceMethods(b *testing.B) {
	service := &Service{} // 简化的服务实例
	ctx := context.Background()

	b.Run("BenchmarkEMSHealth", func(b *testing.B) {
		req := &common.EmptyRequest{}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := service.EMSHealth(ctx, req)
			if err != nil {
				b.Errorf("EMSHealth benchmark failed: %v", err)
			}
		}
	})
}
