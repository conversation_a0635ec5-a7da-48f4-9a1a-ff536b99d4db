package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"

	v1 "gold_store/api/gold_store/v1"
	"gold_store/pkg/ems"
)

// QueryTrack 查询物流轨迹
func (s *Service) QueryTrack(ctx context.Context, req *v1.QueryTrackRequest) (*v1.QueryTrackReply, error) {
	log.Printf("收到查询物流轨迹请求: %+v", req)

	// 创建EMS客户端
	emsClient, err := s.createEMSClient()
	if err != nil {
		return &v1.QueryTrackReply{
			Success: false,
			Message: "创建EMS客户端失败: " + err.Error(),
		}, nil
	}

	// 转换请求格式
	emsReq := &ems.QueryTrackRequest{
		Language:    req.Language,
		WaybillNos:  req.WaybillNos,
		TrackingNos: req.TrackingNos,
	}

	// 初始化最终结果
	tracks := make([]*v1.TrackInfo, 0)

	// 调用EMS API
	emsResp, err := emsClient.QueryTrack(ctx, emsReq)
	if err != nil {
		return &v1.QueryTrackReply{
			Success: false,
			Message: "查询失败: " + err.Error(),
		}, nil
	}

	// 转换标准格式的响应
	for _, track := range emsResp {
		trackDetails := make([]*v1.TrackDetail, 0, len(track.Tracks))
		for _, detail := range track.Tracks {
			trackDetails = append(trackDetails, &v1.TrackDetail{
				TrackTime:   detail.TrackTime,
				TrackStatus: detail.TrackStatus,
				TrackDesc:   detail.TrackDesc,
				Location:    detail.Location,
				Operator:    "", // EMS原始结构中没有这个字段
			})
		}

		tracks = append(tracks, &v1.TrackInfo{
			WaybillNo:  track.WaybillNo,
			TrackingNo: track.TrackingNo,
			Status:     track.Status,
			StatusDesc: track.StatusDesc,
			Tracks:     trackDetails,
		})
	}

	// 如果标准格式没有返回数据，且有运单号，尝试使用有效的格式
	if len(emsResp) == 0 && len(req.WaybillNos) > 0 {
		log.Printf("标准格式无数据，尝试使用有效格式查询运单号: %v", req.WaybillNos)

		// 对每个运单号使用有效格式重新查询
		for _, waybillNo := range req.WaybillNos {
			validFormatReq := map[string]interface{}{
				"waybillNo": waybillNo,
				"direction": "0",
			}

			// 使用TestAPICode方法调用有效格式
			response, err := emsClient.TestAPICode(ctx, ems.ApiCodeQueryTrack, validFormatReq)
			if err != nil {
				log.Printf("有效格式查询失败: %v", err)
				continue
			}

			if response.RetCode == "00000" && len(response.GetRetBodyAsString()) > 0 {
				log.Printf("EMS API返回数据: %s", response.GetRetBodyAsString())

				// 解析有效格式的响应 - EMS返回的格式是 {"responseItems": [...]}
				var responseWrapper struct {
					ResponseItems []struct {
						WaybillNo           string `json:"waybillNo"`
						OpTime              string `json:"opTime"`
						OpName              string `json:"opName"`
						OpDesc              string `json:"opDesc"`
						OpCode              string `json:"opCode"`
						OpOrgCode           string `json:"opOrgCode"`
						OpOrgName           string `json:"opOrgName"`
						OpOrgProvName       string `json:"opOrgProvName"`
						OpOrgCity           string `json:"opOrgCity"`
						OperatorNo          string `json:"operatorNo"`
						OperatorName        string `json:"operatorName"`
						DeliverCode         string `json:"deliverCode,omitempty"`
						AttemptDeliveryCode string `json:"attemptDeliveryCode,omitempty"`
					} `json:"responseItems"`
				}

				// 直接解析RetBody字符串，而不是使用ParseRetBody方法
				retBodyStr := response.GetRetBodyAsString()
				if err := json.Unmarshal([]byte(retBodyStr), &responseWrapper); err == nil && len(responseWrapper.ResponseItems) > 0 {
					// 直接创建完整的TrackDetail列表，包含所有可用字段
					trackDetails := make([]*v1.TrackDetail, 0, len(responseWrapper.ResponseItems))

					for _, item := range responseWrapper.ResponseItems {
						trackDetail := &v1.TrackDetail{
							TrackTime:   item.OpTime,
							TrackStatus: item.OpName,
							TrackDesc:   item.OpDesc,
							Location:    item.OpOrgName,
							Operator:    item.OperatorName, // 操作员姓名
							OpOrgCode:   item.OpOrgCode,    // 操作机构代码
							OpOrgName:   item.OpOrgName,    // 操作机构名称
							OpCode:      item.OpCode,       // 操作代码
							OpName:      item.OpName,       // 操作名称
						}
						trackDetails = append(trackDetails, trackDetail)
					}

					// 直接创建最终的响应格式
					trackInfo := &v1.TrackInfo{
						WaybillNo:  waybillNo,
						TrackingNo: waybillNo,
						Status:     "已处理",
						StatusDesc: "包裹轨迹信息",
						Tracks:     trackDetails,
					}

					// 直接添加到最终结果中，跳过中间转换
					tracks = append(tracks, trackInfo)
					log.Printf("有效格式查询成功，获得 %d 条轨迹记录", len(responseWrapper.ResponseItems))
				} else {
					log.Printf("解析响应数据失败: %v", err)
				}
			}
		}
	}

	return &v1.QueryTrackReply{
		Success: true,
		Message: "查询成功",
		Tracks:  tracks,
	}, nil
}

// QueryWaybill 查询运单
func (s *Service) QueryWaybill(ctx context.Context, req *v1.QueryWaybillRequest) (*v1.QueryWaybillReply, error) {
	log.Printf("收到查询运单请求: %+v", req)

	// 创建EMS客户端
	emsClient, err := s.createEMSClient()
	if err != nil {
		return &v1.QueryWaybillReply{
			Success: false,
			Message: "创建EMS客户端失败: " + err.Error(),
		}, nil
	}

	// 转换请求格式
	emsReq := &ems.QueryWaybillRequest{
		Language:    req.Language,
		WaybillNos:  req.WaybillNos,
		TrackingNos: req.TrackingNos,
	}

	// 调用EMS API
	emsResp, err := emsClient.QueryWaybill(ctx, emsReq)
	if err != nil {
		return &v1.QueryWaybillReply{
			Success: false,
			Message: "查询失败: " + err.Error(),
		}, nil
	}

	// 转换响应格式
	waybills := make([]*v1.WaybillInfo, 0, len(emsResp))
	for _, waybill := range emsResp {
		waybills = append(waybills, &v1.WaybillInfo{
			WaybillNo:  waybill.WaybillNo,
			TrackingNo: waybill.TrackingNo,
			Status:     waybill.Status,
			StatusDesc: waybill.StatusDesc,
			UpdateTime: waybill.UpdateTime,
		})
	}

	return &v1.QueryWaybillReply{
		Success:  true,
		Message:  "查询成功",
		Waybills: waybills,
	}, nil
}

// QueryFee 查询费用
func (s *Service) QueryFee(ctx context.Context, req *v1.QueryFeeRequest) (*v1.QueryFeeReply, error) {
	log.Printf("收到查询费用请求: %+v", req)

	// 创建EMS客户端
	emsClient, err := s.createEMSClient()
	if err != nil {
		return &v1.QueryFeeReply{
			Success: false,
			Message: "创建EMS客户端失败: " + err.Error(),
		}, nil
	}

	// 转换请求格式
	cargo := make([]ems.CargoInfo, 0, len(req.Cargo))
	for _, c := range req.Cargo {
		cargo = append(cargo, ems.CargoInfo{
			Name:        c.Name,
			Quantity:    int(c.Quantity),
			Weight:      int(c.Weight),
			Value:       int(c.Value),
			Currency:    c.Currency,
			Origin:      c.Origin,
			Description: c.Description,
		})
	}

	emsReq := &ems.QueryFeeRequest{
		Language:        req.Language,
		ServiceType:     req.ServiceType,
		SenderCountry:   req.SenderCountry,
		ReceiverCountry: req.ReceiverCountry,
		Weight:          int(req.Weight),
		Cargo:           cargo,
	}

	// 调用EMS API
	emsResp, err := emsClient.QueryFee(ctx, emsReq)
	if err != nil {
		return &v1.QueryFeeReply{
			Success: false,
			Message: "查询失败: " + err.Error(),
		}, nil
	}

	// 转换响应格式
	return &v1.QueryFeeReply{
		Success: true,
		Message: "查询成功",
		Fee: &v1.FeeInfo{
			ServiceType:   emsResp.ServiceType,
			ServiceDesc:   emsResp.ServiceDesc,
			TotalFee:      int32(emsResp.TotalFee),
			Currency:      emsResp.Currency,
			EstimatedDays: int32(emsResp.EstimatedDays),
		},
	}, nil
}

// HandlePushNotification 处理推送通知
func (s *Service) HandlePushNotification(ctx context.Context, req *v1.PushNotificationRequest) (*v1.PushNotificationReply, error) {
	log.Printf("收到推送通知: %+v", req)

	// 创建EMS客户端
	emsClient, err := s.createEMSClient()
	if err != nil {
		return &v1.PushNotificationReply{
			SerialNo:    req.SerialNo,
			Code:        "S00001",
			CodeMessage: "创建EMS客户端失败: " + err.Error(),
			SenderNo:    req.SenderNo,
		}, nil
	}

	// 转换请求格式
	pushData := &ems.PushNotificationData{
		Method:           req.Method,
		SenderNo:         req.SenderNo,
		MsgType:          req.MsgType,
		TimeStamp:        req.TimeStamp,
		Version:          req.Version,
		LogitcsInterface: req.LogitcsInterface,
		SerialNo:         req.SerialNo,
	}

	// 处理推送通知
	emsResp, err := emsClient.HandlePushNotification(ctx, pushData)
	if err != nil {
		return &v1.PushNotificationReply{
			SerialNo:    req.SerialNo,
			Code:        "S00001",
			CodeMessage: "处理失败: " + err.Error(),
			SenderNo:    req.SenderNo,
		}, nil
	}

	// 转换响应格式
	return &v1.PushNotificationReply{
		SerialNo:    emsResp.SerialNo,
		Code:        emsResp.Code,
		CodeMessage: emsResp.CodeMessage,
		SenderNo:    emsResp.SenderNo,
	}, nil
}

// createEMSClient 创建EMS客户端
func (s *Service) createEMSClient() (*ems.Client, error) {
	// 从配置中获取EMS配置
	if s.business == nil || s.business.EMS == nil {
		return nil, fmt.Errorf("EMS配置未找到")
	}

	// 转换配置格式
	configData := &ems.EMSConfigData{
		Environment:   s.business.EMS.Environment,
		SenderNo:      s.business.EMS.SenderNo,
		Authorization: s.business.EMS.Authorization,
		SecretKey:     s.business.EMS.SecretKey,
		UserCode:      s.business.EMS.UserCode,
		Timeout:       s.business.EMS.Timeout,
	}

	// 使用配置创建客户端
	return ems.NewClientFromConfig(configData)
}
